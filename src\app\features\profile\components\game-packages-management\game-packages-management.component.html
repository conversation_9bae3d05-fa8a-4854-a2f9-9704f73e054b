<div class="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 p-4 lg:p-6">
  <div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-2xl lg:text-3xl font-bold text-white mb-2">Package Management</h1>
      <p class="text-gray-300 text-sm lg:text-base">Manage game packages and subscriptions</p>
    </div>

    <!-- Controls -->
    <div class="bg-slate-800/50 backdrop-blur-sm rounded-lg p-4 lg:p-6 mb-6">
      <div class="flex flex-col lg:flex-row gap-4 lg:items-center lg:justify-between">
        <!-- Search and Sort -->
        <div class="flex flex-col sm:flex-row gap-3 flex-1">
          <div class="relative flex-1 max-w-md">
            <input
              type="text"
              [(ngModel)]="searchTerm"
              (input)="onSearchChange()"
              placeholder="Search packages..."
              class="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
            <svg class="absolute right-3 top-2.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
          
          <select
            [(ngModel)]="sortBy"
            (change)="onSortChange()"
            class="bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="-id">Newest First</option>
            <option value="id">Oldest First</option>
            <option value="name">Name A-Z</option>
            <option value="-name">Name Z-A</option>
            <option value="price">Price Low-High</option>
            <option value="-price">Price High-Low</option>
          </select>
        </div>

        <!-- Add Package Button -->
        <button
          (click)="showAddPackageForm()"
          class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-2"
        >
          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
          Add Package
        </button>
      </div>
    </div>

    <!-- Add Package Form -->
    <div *ngIf="showAddForm" class="bg-slate-800/50 backdrop-blur-sm rounded-lg p-4 lg:p-6 mb-6">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold text-white">Add New Package</h2>
        <button
          (click)="hideAddPackageForm()"
          class="text-gray-400 hover:text-white transition-colors"
        >
          <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <form (ngSubmit)="addPackage()" class="space-y-4">
        <!-- Error Message -->
        <div *ngIf="addPackageError" class="bg-red-500/20 border border-red-500/50 rounded-lg p-3">
          <p class="text-red-400 text-sm">{{ addPackageError }}</p>
        </div>

        <!-- Basic Info -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2">Package Name *</label>
            <input
              type="text"
              [(ngModel)]="newPackage.name"
              name="name"
              required
              class="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter package name"
            >
          </div>

          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2">Price *</label>
            <input
              type="number"
              [(ngModel)]="newPackage.price"
              name="price"
              step="0.01"
              min="0"
              required
              class="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="0.00"
            >
          </div>
        </div>

        <div>
          <label class="block text-gray-300 text-sm font-medium mb-2">Description *</label>
          <textarea
            [(ngModel)]="newPackage.description"
            name="description"
            required
            rows="3"
            class="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            placeholder="Enter package description"
          ></textarea>
        </div>

        <!-- Benefits -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2">Benefit 1</label>
            <input
              type="text"
              [(ngModel)]="newPackage.benefit_1"
              name="benefit_1"
              class="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="First benefit"
            >
          </div>

          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2">Benefit 2</label>
            <input
              type="text"
              [(ngModel)]="newPackage.benefit_2"
              name="benefit_2"
              class="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Second benefit"
            >
          </div>

          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2">Benefit 3</label>
            <input
              type="text"
              [(ngModel)]="newPackage.benefit_3"
              name="benefit_3"
              class="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Third benefit"
            >
          </div>
        </div>

        <!-- Duration and Max Games -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2">Duration (Days) *</label>
            <input
              type="number"
              [(ngModel)]="newPackage.duration_days"
              name="duration_days"
              min="1"
              required
              class="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
          </div>

          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2">Max Selectable Games *</label>
            <input
              type="number"
              [(ngModel)]="newPackage.max_selectable_games"
              name="max_selectable_games"
              min="1"
              required
              class="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
          </div>
        </div>

        <!-- Game Selection -->
        <div>
          <label class="block text-gray-300 text-sm font-medium mb-2">Select Games</label>
          <div *ngIf="gamesLoading" class="text-gray-400 text-sm">Loading games...</div>
          <div *ngIf="!gamesLoading" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 max-h-48 overflow-y-auto bg-slate-700/30 rounded-lg p-3">
            <label *ngFor="let game of availableGames" class="flex items-center space-x-2 cursor-pointer hover:bg-slate-600/30 rounded p-2">
              <input
                type="checkbox"
                [checked]="isGameSelected(game.id)"
                (change)="onGameSelectionChange(game.id, $event)"
                class="rounded border-slate-500 text-blue-600 focus:ring-blue-500 focus:ring-offset-0"
              >
              <span class="text-white text-sm">{{ game.title }}</span>
            </label>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex gap-3 pt-4">
          <button
            type="submit"
            [disabled]="addPackageLoading"
            class="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-600/50 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-2"
          >
            <div *ngIf="addPackageLoading" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            {{ addPackageLoading ? 'Creating...' : 'Create Package' }}
          </button>
          
          <button
            type="button"
            (click)="hideAddPackageForm()"
            class="bg-slate-600 hover:bg-slate-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>

    <!-- Loading State -->
    <div *ngIf="packagesLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
    </div>

    <!-- Error State -->
    <div *ngIf="packagesError && !packagesLoading" class="bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-6">
      <p class="text-red-400">{{ packagesError }}</p>
    </div>

    <!-- Packages Grid -->
    <div *ngIf="!packagesLoading && !packagesError" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
      <div *ngFor="let package of packages" class="bg-slate-800/50 backdrop-blur-sm rounded-lg p-4 hover:bg-slate-800/70 transition-colors cursor-pointer"
           (click)="openPackageDetail(package.id)">
        <div class="flex justify-between items-start mb-3">
          <h3 class="text-lg font-semibold text-white">{{ package.name }}</h3>
          <span class="text-blue-400 font-bold text-lg">{{ package.price }}₸</span>
        </div>
        
        <p class="text-gray-300 text-sm mb-3 line-clamp-2">{{ package.description }}</p>
        
        <div class="flex items-center justify-between text-xs text-gray-400 mb-3">
          <span>{{ package.duration_days }} days</span>
          <span>Max {{ package.max_selectable_games }} games</span>
        </div>
        
        <div class="flex flex-wrap gap-1 mb-3">
          <span *ngFor="let game of package.games" 
                class="bg-blue-600/20 text-blue-400 px-2 py-1 rounded text-xs">
            {{ game.title }}
          </span>
        </div>
        
        <div class="flex justify-between items-center">
          <span class="text-xs text-gray-500">{{ package.games.length }} games</span>
          <button
            (click)="deletePackage(package.id); $event.stopPropagation()"
            class="text-red-400 hover:text-red-300 transition-colors"
            title="Delete package"
          >
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="!packagesLoading && !packagesError && packages.length === 0" class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
      </svg>
      <h3 class="text-lg font-medium text-white mb-2">No packages found</h3>
      <p class="text-gray-400 mb-4">Get started by creating your first package.</p>
      <button
        (click)="showAddPackageForm()"
        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
      >
        Add Package
      </button>
    </div>

    <!-- Pagination -->
    <div *ngIf="!packagesLoading && packages.length > 0 && getTotalPages() > 1" 
         class="flex justify-center items-center space-x-2">
      <button
        (click)="onPageChange(currentPage - 1)"
        [disabled]="!hasPrevious"
        class="px-3 py-2 text-sm font-medium text-gray-300 bg-slate-700 rounded-lg hover:bg-slate-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        Previous
      </button>
      
      <button
        *ngFor="let page of getPageNumbers()"
        (click)="onPageChange(page)"
        [class.bg-blue-600]="page === currentPage"
        [class.bg-slate-700]="page !== currentPage"
        class="px-3 py-2 text-sm font-medium text-white rounded-lg hover:bg-blue-500 transition-colors"
      >
        {{ page }}
      </button>
      
      <button
        (click)="onPageChange(currentPage + 1)"
        [disabled]="!hasNext"
        class="px-3 py-2 text-sm font-medium text-gray-300 bg-slate-700 rounded-lg hover:bg-slate-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        Next
      </button>
    </div>
  </div>

  <!-- Package Detail Modal -->
  <app-game-package-detail
    *ngIf="showPackageDetail && selectedPackageId"
    [packageId]="selectedPackageId"
    (close)="closePackageDetail()"
    (packageUpdated)="onPackageUpdated($event)"
    (packageDeleted)="onPackageDeleted($event)"
  ></app-game-package-detail>
</div>
