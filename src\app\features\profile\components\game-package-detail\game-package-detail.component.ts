import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import { GamePackageService } from '../../../../core/services/game-package.service';
import { GameService } from '../../../../core/services/game.service';
import { ModalService } from '../../../../core/services/modal.service';
import { 
  GamePackage, 
  UpdateGamePackageRequest 
} from '../../../../core/models/game-package.model';
import { Game } from '../../../../core/models/game.model';

@Component({
  selector: 'app-game-package-detail',
  standalone: false,
  templateUrl: './game-package-detail.component.html',
  styleUrl: './game-package-detail.component.css'
})
export class GamePackageDetailComponent implements OnInit, OnDestroy {
  @Input() packageId!: number;
  @Output() close = new EventEmitter<void>();
  @Output() packageUpdated = new EventEmitter<GamePackage>();
  @Output() packageDeleted = new EventEmitter<number>();

  package: GamePackage | null = null;
  packageLoading = false;
  packageError = '';

  // Edit mode
  isEditing = false;
  editPackageLoading = false;
  editPackageError = '';
  editPackage: UpdateGamePackageRequest = {};

  // Available games for selection
  availableGames: Game[] = [];
  gamesLoading = false;
  selectedGameIds: number[] = [];

  private subscriptions: Subscription[] = [];

  constructor(
    private packageService: GamePackageService,
    private gameService: GameService,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.loadPackage();
    this.loadAvailableGames();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  loadPackage(): void {
    this.packageLoading = true;
    this.packageError = '';

    this.packageService.getGamePackage(this.packageId).subscribe({
      next: (packageData) => {
        this.package = packageData;
        this.packageLoading = false;
        this.initializeEditForm();
      },
      error: (error) => {
        this.packageError = error.message || 'Failed to load package';
        this.packageLoading = false;
      }
    });
  }

  loadAvailableGames(): void {
    this.gamesLoading = true;
    this.gameService.getGames({}, 1, 100).subscribe({
      next: (response) => {
        this.availableGames = response.results;
        this.gamesLoading = false;
      },
      error: (error) => {
        console.error('Failed to load games:', error);
        this.gamesLoading = false;
      }
    });
  }

  initializeEditForm(): void {
    if (!this.package) return;

    this.editPackage = {
      name: this.package.name,
      description: this.package.description,
      benefit_1: this.package.benefit_1,
      benefit_2: this.package.benefit_2,
      benefit_3: this.package.benefit_3,
      price: this.package.price,
      duration_days: this.package.duration_days,
      max_selectable_games: this.package.max_selectable_games,
      game_ids: this.package.games.map(game => game.id)
    };
    this.selectedGameIds = [...this.editPackage.game_ids || []];
  }

  toggleEditMode(): void {
    this.isEditing = !this.isEditing;
    if (this.isEditing) {
      this.initializeEditForm();
    }
    this.editPackageError = '';
  }

  onGameSelectionChange(gameId: number, event: any): void {
    if (event.target.checked) {
      if (!this.selectedGameIds.includes(gameId)) {
        this.selectedGameIds.push(gameId);
      }
    } else {
      this.selectedGameIds = this.selectedGameIds.filter(id => id !== gameId);
    }
    this.editPackage.game_ids = [...this.selectedGameIds];
  }

  isGameSelected(gameId: number): boolean {
    return this.selectedGameIds.includes(gameId);
  }

  savePackage(): void {
    if (!this.package || !this.editPackage) return;

    // Validate required fields
    if (!this.editPackage.name?.trim()) {
      this.editPackageError = 'Package name is required';
      return;
    }

    if (!this.editPackage.description?.trim()) {
      this.editPackageError = 'Package description is required';
      return;
    }

    if (!this.editPackage.price?.toString().trim()) {
      this.editPackageError = 'Package price is required';
      return;
    }

    // Validate price format
    const priceNum = parseFloat(this.editPackage.price.toString());
    if (isNaN(priceNum) || priceNum <= 0) {
      this.editPackageError = 'Please enter a valid price';
      return;
    }

    if (!this.editPackage.duration_days || this.editPackage.duration_days <= 0) {
      this.editPackageError = 'Duration must be greater than 0';
      return;
    }

    if (!this.editPackage.max_selectable_games || this.editPackage.max_selectable_games <= 0) {
      this.editPackageError = 'Max selectable games must be greater than 0';
      return;
    }

    this.editPackageLoading = true;
    this.editPackageError = '';

    // Prepare package data
    const packageData: UpdateGamePackageRequest = {
      ...this.editPackage,
      price: priceNum.toFixed(2)
    };

    this.packageService.updateGamePackage(this.package.id, packageData).subscribe({
      next: (updatedPackage) => {
        this.package = updatedPackage;
        this.packageUpdated.emit(updatedPackage);
        this.isEditing = false;
        this.editPackageLoading = false;
        this.modalService.success('Success', 'Package updated successfully!');
      },
      error: (error) => {
        this.editPackageError = error.message || 'Failed to update package';
        this.editPackageLoading = false;
      }
    });
  }

  deletePackage(): void {
    if (!this.package) return;

    this.modalService.confirm(
      'Delete Package',
      'Are you sure you want to delete this package? This action cannot be undone.',
      'Delete',
      'Cancel'
    ).then(confirmed => {
      if (confirmed && this.package) {
        this.packageService.deleteGamePackage(this.package.id).subscribe({
          next: () => {
            this.packageDeleted.emit(this.package!.id);
            this.modalService.success('Success', 'Package deleted successfully!');
          },
          error: (error) => {
            this.modalService.error('Error', 'Failed to delete package: ' + error.message);
          }
        });
      }
    });
  }

  closeModal(): void {
    this.close.emit();
  }

  // Prevent modal from closing when clicking inside
  onModalContentClick(event: Event): void {
    event.stopPropagation();
  }
}
